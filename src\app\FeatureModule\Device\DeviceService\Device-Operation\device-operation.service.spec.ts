import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject, of, throwError } from 'rxjs';
import { DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_CONVERT_TO_CLIENT, DEVICE_CONVERT_TO_DEMO, DEVICE_CONVERT_TO_TEST, DeviceDetailResource, DeviceListResource, Device_Select_Message } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { AssignSelectedReleaseVersionRequest } from 'src/app/model/device/AssignSelectedReleaseVersionRequest.model';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { ReleaseVersionRequest } from 'src/app/model/release-version-request.model';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DeviceOperationService } from './device-operation.service';
import { DeviceService } from 'src/app/shared/device.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';

describe('DeviceOperationService', () => {
  let service: DeviceOperationService;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingService: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let downloadService: jasmine.SpyObj<DownloadService>;
  let moduleValidationService: jasmine.SpyObj<ModuleValidationServiceService>;

  beforeEach(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', [
      'getpackageVersion', 'getDeviceList', 'updateDeviceTypeToTest', 'updateDeviceTypeToClient',
      'updateDeviceTypeToDemo', 'updateDeviceState', 'editEnableDisableForDevice',
      'disableProductStatusForDevice', 'rmaProductStatusForDevice', 'associationDeviceWithSalesOrder',
      'generateCSVFileForDevice', 'downloadCSVFileForDevice', 'getDeviceDetail',
      'getReleaseVersionDetail', 'assignSelectedReleaseVersion'
    ]);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkForNull', 'checkValueIsNullOrEmpty', 'checkNullFieldValue', 'getIdsFromArray',
      'getSelectedValueFromEnum', 'getSelectedValueFromBooleanKeyValueMapping', 'getDeviceTypeStringToEnum'
    ]);
    const exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const downloadServiceSpy = jasmine.createSpyObj('DownloadService', ['downloadExportCSV']);
    const moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditableWithMultipalRecoard', 'validateWithUserCountryForMultileRecord',
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);

    TestBed.configureTestingModule({
      providers: [
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        commonsProviders(toastrServiceMock)
      ]
    });

    service = TestBed.inject(DeviceOperationService);
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    downloadService = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    moduleValidationService = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get device list filter request parameter subject', () => {
    expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should get device list refresh subject', () => {
    expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should set and get package version list', () => {
    const packageVersions = ['v1.0.0', 'v1.1.0', 'v2.0.0'];
    service.setPackageVersionList(packageVersions);
    expect(service.getPackageVersionList()).toEqual(packageVersions);
  });

  it('should set and get sales order number list', () => {
    const salesOrderNumbers = ['SO001', 'SO002', 'SO003'];
    service.setSalesOrderNumberList(salesOrderNumbers);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
  });

  it('should set and get country list', () => {
    const countries: CountryListResponse[] = [
      { id: 1, country: 'USA', languages: ['English'] },
      { id: 2, country: 'Canada', languages: ['English', 'French'] }
    ];
    service.setCountryList(countries);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should initialize with empty cache arrays', () => {
    expect(service.getPackageVersionList()).toEqual([]);
    expect(service.getSalesOrderNumberList()).toEqual([]);
    expect(service.getCountryList()).toEqual([]);
  });

  it('should maintain separate cache for each data type', () => {
    const packageVersions = ['v1.0.0'];
    const salesOrderNumbers = ['SO001'];
    const countries: CountryListResponse[] = [{ id: 1, country: 'USA', languages: ['English'] }];

    service.setPackageVersionList(packageVersions);
    service.setSalesOrderNumberList(salesOrderNumbers);
    service.setCountryList(countries);

    expect(service.getPackageVersionList()).toEqual(packageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should allow updating cached data', () => {
    // Set initial data
    service.setPackageVersionList(['v1.0.0']);
    expect(service.getPackageVersionList()).toEqual(['v1.0.0']);

    // Update with new data
    service.setPackageVersionList(['v1.0.0', 'v2.0.0']);
    expect(service.getPackageVersionList()).toEqual(['v1.0.0', 'v2.0.0']);
  });

  it('should update cache in background with fresh API data', async () => {
    // Setup API responses
    const apiPackageVersions = ['v3.0.0', 'v4.0.0'];
    const apiSalesOrders = ['SO004', 'SO005'];
    const apiCountries: CountryListResponse[] = [
      { id: 3, country: 'Germany', languages: ['German'] }
    ];

    deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
    commonsService.checkForNull.and.returnValue(apiPackageVersions);

    // Call updateCacheInBackground
    await service.updateCacheInBackground();

    // Verify cache is updated with fresh data
    expect(service.getPackageVersionList()).toEqual(apiPackageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(apiSalesOrders);
    expect(service.getCountryList()).toEqual(apiCountries);

    // Verify API calls were made
    expect(deviceService.getpackageVersion).toHaveBeenCalled();
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
  });

  it('should update only sales order cache when updateSalesOrderCacheOnly is called', async () => {
    // Setup initial cache with some data
    service.setPackageVersionList(['v1.0.0']);
    service.setSalesOrderNumberList(['SO001']);
    service.setCountryList([{ id: 1, country: 'USA', languages: ['English'] }]);

    // Setup API response for sales orders only
    const newSalesOrders = ['SO001', 'SO002', 'SO003'];
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(newSalesOrders));

    // Call updateSalesOrderCacheOnly
    await service.updateSalesOrderCacheOnly();

    // Verify only sales order cache is updated
    expect(service.getSalesOrderNumberList()).toEqual(newSalesOrders);

    // Verify other caches remain unchanged
    expect(service.getPackageVersionList()).toEqual(['v1.0.0']);
    expect(service.getCountryList()).toEqual([{ id: 1, country: 'USA', languages: ['English'] }]);

    // Verify only sales order API was called
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
  });

  describe('callDeviceListFilterRequestParameterSubject', () => {
    it('should emit device filter action through subject', () => {
      const deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);

      spyOn(service.getDeviceListFilterRequestParameterSubject(), 'next');

      service.callDeviceListFilterRequestParameterSubject(deviceFilterAction);

      expect(service.getDeviceListFilterRequestParameterSubject().next).toHaveBeenCalledWith(deviceFilterAction);
    });
  });

  describe('callDeviceListRefreshSubject', () => {
    it('should emit refresh parameters through subject', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service.getDeviceListRefreshSubject(), 'next');

      service.callDeviceListRefreshSubject(listingPageReloadSubjectParameter);

      expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
    });
  });

  describe('callRefreshPageSubject', () => {
    it('should call device list filter when resource is DeviceListResource and filter is hidden', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      listingPageReloadSubjectParameter.isClearFilter = false;
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.any(DeviceFilterAction)
      );
    });

    it('should call device list filter with empty search request when isClearFilter is true', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      const expectedDeviceFilterAction = jasmine.objectContaining({
        deviceSearchRequest: jasmine.objectContaining({
          packageVersions: null
        })
      });

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
    });

    it('should call device list filter with empty search request when deviceSearchRequestBodyApply is null', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        null
      );

      const expectedDeviceFilterAction = jasmine.objectContaining({
        deviceSearchRequest: jasmine.objectContaining({
          packageVersions: null
        })
      });

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
    });

    it('should call device list refresh subject when filter is not hidden', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service.getDeviceListRefreshSubject(), 'next');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        false,
        deviceSearchRequest
      );

      expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
    });

    it('should not call any subject when resource is not DeviceListResource', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');
      spyOn(service.getDeviceListRefreshSubject(), 'next');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        'OTHER_RESOURCE',
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).not.toHaveBeenCalled();
      expect(service.getDeviceListRefreshSubject().next).not.toHaveBeenCalled();
    });
  });

  describe('Edge cases and boundary conditions', () => {
    it('should handle empty arrays in cache setters', () => {
      service.setPackageVersionList([]);
      service.setSalesOrderNumberList([]);
      service.setCountryList([]);

      expect(service.getPackageVersionList()).toEqual([]);
      expect(service.getSalesOrderNumberList()).toEqual([]);
      expect(service.getCountryList()).toEqual([]);
    });

    it('should handle large arrays in cache', () => {
      const largePackageVersions = Array.from({ length: 1000 }, (_, i) => `v${i}.0.0`);
      const largeSalesOrders = Array.from({ length: 1000 }, (_, i) => `SO${i.toString().padStart(6, '0')}`);
      const largeCountries = Array.from({ length: 200 }, (_, i) => ({ id: i, country: `Country${i}`, languages: ['Language'] }));

      service.setPackageVersionList(largePackageVersions);
      service.setSalesOrderNumberList(largeSalesOrders);
      service.setCountryList(largeCountries);

      expect(service.getPackageVersionList()).toEqual(largePackageVersions);
      expect(service.getSalesOrderNumberList()).toEqual(largeSalesOrders);
      expect(service.getCountryList()).toEqual(largeCountries);
    });

    it('should handle undefined deviceSearchRequestBodyApply in callRefreshPageSubject', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        undefined as any
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          deviceSearchRequest: jasmine.objectContaining({
            packageVersions: null
          })
        })
      );
    });
  });

  describe('Private method coverage through public methods', () => {
    describe('getPackageVersionsFromAPI coverage', () => {
      it('should handle successful package version API call', async () => {
        const mockResponse = { body: ['v1.0.0', 'v2.0.0'] };
        deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(['v1.0.0', 'v2.0.0']);

        await service.updateCacheInBackground();

        expect(deviceService.getpackageVersion).toHaveBeenCalled();
        expect(commonsService.checkForNull).toHaveBeenCalledWith(['v1.0.0', 'v2.0.0']);
        expect(service.getPackageVersionList()).toEqual(['v1.0.0', 'v2.0.0']);
      });

      it('should handle null response body from package version API', async () => {
        const mockResponse = { body: null };
        deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(null);

        await service.updateCacheInBackground();

        expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
        expect(service.getPackageVersionList()).toEqual([]);
      });

      it('should handle undefined response body from package version API', async () => {
        const mockResponse = { body: undefined };
        deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(undefined);

        await service.updateCacheInBackground();

        expect(commonsService.checkForNull).toHaveBeenCalledWith(undefined);
        expect(service.getPackageVersionList()).toEqual([]);
      });
    });

    describe('getSalesOrderNumbersFromAPI coverage', () => {
      it('should handle successful sales order API call', async () => {
        const mockSalesOrders = ['SO001', 'SO002', 'SO003'];
        deviceService.getpackageVersion.and.returnValue(of({ body: ['v1.0.0'] } as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(['v1.0.0']);

        await service.updateCacheInBackground();

        expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
        expect(service.getSalesOrderNumberList()).toEqual(mockSalesOrders);
      });
    });

    describe('getCountriesFromAPI coverage', () => {
      it('should handle successful countries API call', async () => {
        const mockCountries = [
          { id: 1, country: 'USA', languages: ['English'] },
          { id: 2, country: 'Canada', languages: ['English', 'French'] }
        ];
        deviceService.getpackageVersion.and.returnValue(of({ body: ['v1.0.0'] } as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountries));
        commonsService.checkForNull.and.returnValue(['v1.0.0']);

        await service.updateCacheInBackground();

        expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
        expect(service.getCountryList()).toEqual(mockCountries);
      });
    });
  });

  describe('updateSalesOrderCacheOnly method coverage', () => {
    it('should call getSalesOrderNumbersFromAPI and update cache', async () => {
      const mockSalesOrders = ['SO100', 'SO101', 'SO102'];
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));

      await service.updateSalesOrderCacheOnly();

      expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
      expect(service.getSalesOrderNumberList()).toEqual(mockSalesOrders);
    });
  });

  describe('Complete line coverage for callRefreshPageSubject', () => {
    it('should cover the line where deviceSearchRequest is assigned from deviceSearchRequestBodyApply', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], 'active', 'type1', 'device123', 'serial123', 'customer1', [1, 2], true, false, ['SO001'], null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          deviceSearchRequest: deviceSearchRequest
        })
      );
    });

    it('should cover the line where new DeviceFilterAction is created', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.any(DeviceFilterAction)
      );
    });
  });

  describe('Constructor and initialization coverage', () => {
    it('should initialize with injected dependencies', () => {
      expect(service).toBeTruthy();
      expect(service.getPackageVersionList()).toEqual([]);
      expect(service.getSalesOrderNumberList()).toEqual([]);
      expect(service.getCountryList()).toEqual([]);
      expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
      expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
    });
  });

  describe('Additional coverage for uncovered statements', () => {

    it('should handle commonsService.checkForNull returning null in getPackageVersionsFromAPI', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return null to test the || [] fallback
      commonsService.checkForNull.and.returnValue(null);

      await service.updateCacheInBackground();

      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle commonsService.checkForNull returning undefined in getPackageVersionsFromAPI', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return undefined to test the || [] fallback
      commonsService.checkForNull.and.returnValue(undefined);

      await service.updateCacheInBackground();

      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle response with undefined body in getPackageVersionsFromAPI', async () => {
      const mockResponse = { body: undefined };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      commonsService.checkForNull.and.returnValue(undefined);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(undefined);
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle response with no body property in getPackageVersionsFromAPI', async () => {
      const mockResponse = {};
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      commonsService.checkForNull.and.returnValue(undefined);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(undefined);
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle the exact line where commonsService.checkForNull returns falsy value', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return a falsy value (empty array)
      commonsService.checkForNull.and.returnValue([]);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(['v1.0.0']);
      // The || [] should kick in when checkForNull returns empty array
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle the exact line where commonsService.checkForNull returns null as any[]', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return null (cast as any[] to satisfy TypeScript)
      commonsService.checkForNull.and.returnValue(null as any);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(['v1.0.0']);
      // The || [] should kick in when checkForNull returns null
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should test the specific conditional branch in getPackageVersionsFromAPI', async () => {
      // Test the specific line: return this.commonsService.checkForNull(response?.body) || [];
      const mockResponse = { body: null };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));

      // Make commonsService.checkForNull return null to test the || [] fallback
      commonsService.checkForNull.and.returnValue(null as any);

      // Call the method directly through updateCacheInBackground to test the private method
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
      expect(service.getPackageVersionList()).toEqual([]);
    });
  });

  // ==================== NEW BUSINESS LOGIC TESTS ====================

  describe('Device List Operations', () => {
    describe('loadDeviceList', () => {
      it('should load device list successfully', async () => {
        const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        const mockPageObj = { page: 0, size: 10 };
        const mockResponse = {
          status: 200,
          body: {
            content: [{ id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED' }],
            numberOfElements: 1,
            totalElements: 1
          }
        };

        deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

        expect(result.success).toBe(true);
        expect(result.devices).toEqual(mockResponse.body.content);
        expect(result.totalDeviceDisplay).toBe(1);
        expect(result.totalDevice).toBe(1);
        expect(result.localDeviceList).toEqual([{
          id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED'
        }]);
      });

      it('should handle non-200 response', async () => {
        const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        const mockPageObj = { page: 0, size: 10 };
        const mockResponse = { status: 404, body: null };

        deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

        expect(result.success).toBe(false);
        expect(result.devices).toEqual([]);
        expect(result.totalDeviceDisplay).toBe(0);
        expect(result.totalDevice).toBe(0);
        expect(result.localDeviceList).toEqual([]);
      });

      it('should handle API error', async () => {
        const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        const mockPageObj = { page: 0, size: 10 };

        deviceService.getDeviceList.and.returnValue(throwError(() => new Error('API Error')));

        await expectAsync(service.loadDeviceList(mockSearchRequest, mockPageObj)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('exportDeviceCSV', () => {
      it('should export device CSV successfully', async () => {
        const mockDeviceIds = [1, 2, 3];
        const mockGenerateResponse = { body: { fileName: 'devices.csv' } };
        const mockDownloadResponse = { body: 'csv,data' };

        deviceService.generateCSVFileForDevice.and.returnValue(of(mockGenerateResponse as any));
        deviceService.downloadCSVFileForDevice.and.returnValue(of(mockDownloadResponse as any));

        await service.exportDeviceCSV(mockDeviceIds);

        expect(deviceService.generateCSVFileForDevice).toHaveBeenCalled();
        expect(deviceService.downloadCSVFileForDevice).toHaveBeenCalledWith('devices.csv');
        expect(downloadService.downloadExportCSV).toHaveBeenCalledWith('List_of_Device(s).xls', mockDownloadResponse);
      });

      it('should handle export error', async () => {
        const mockDeviceIds = [1, 2, 3];

        deviceService.generateCSVFileForDevice.and.returnValue(throwError(() => new Error('Export Error')));

        await expectAsync(service.exportDeviceCSV(mockDeviceIds)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });
  });

  describe('Device Type Conversion Operations', () => {
    describe('convertDevicesToTest', () => {
      it('should convert devices to test type successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' },
          { deviceType: 'DEMO_DEVICE', editable: true, country: 'USA' }
        ];
        const mockResponse = { status: 200, body: { message: 'Success' } };

        deviceService.updateDeviceTypeToTest.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);

        await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
        expect(deviceService.updateDeviceTypeToTest).toHaveBeenCalledWith(mockDeviceIds);
        expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_TEST);
        expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
      });

      it('should show info message when devices are already test type', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ deviceType: 'TEST_DEVICE', editable: true, country: 'USA' }];
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);
        await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('This device is already Test device');
        expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
      });

      it('should handle conversion error', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' }];

        deviceService.updateDeviceTypeToTest.and.returnValue(throwError(() => new Error('Conversion Error')));
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);

        await expectAsync(service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('convertDevicesToClient', () => {
      it('should convert devices to client type successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ deviceType: 'TEST_DEVICE', editable: true, country: 'USA' }];
        const mockResponse = { status: 200, body: { message: 'Success' } };

        deviceService.updateDeviceTypeToClient.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);

        await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
        expect(deviceService.updateDeviceTypeToClient).toHaveBeenCalledWith(mockDeviceIds);
        expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_CLIENT);
      });

      it('should show info message when devices are already client type', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' }];
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);
        await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);
        expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_CLIENT);
        expect(deviceService.updateDeviceTypeToClient).not.toHaveBeenCalled();
      });
    });

    describe('convertDevicesToDemo', () => {
      it('should convert devices to demo type successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ deviceType: 'TEST_DEVICE', editable: true, country: 'USA' }];
        const mockResponse = { status: 200, body: { message: 'Success' } };

        deviceService.updateDeviceTypeToDemo.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);

        await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
        expect(deviceService.updateDeviceTypeToDemo).toHaveBeenCalledWith(mockDeviceIds);
        expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_DEMO);
      });

      it('should show info message when devices are already demo type', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ deviceType: 'DEMO_DEVICE', editable: true, country: 'USA' }];
        spyOn(service, 'validateDevicePermissions').and.returnValue(true);
        await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);
        expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_DEMO);
        expect(deviceService.updateDeviceTypeToDemo).not.toHaveBeenCalled();
      });
    });
  });

  describe('Device State Management Operations', () => {
    describe('lockUnlockDevices', () => {
      it('should lock/unlock devices successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { locked: false, editable: true, country: 'USA' },
          { locked: false, editable: true, country: 'USA' }
        ];
        const mockResponse = { status: 200, body: { message: 'Devices locked successfully' } };

        deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');
        spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);

        await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

        expect(service.validateDeviceLockUnlockPermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
        expect(deviceService.updateDeviceState).toHaveBeenCalledWith(mockDeviceIds, true);
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices locked successfully');
        expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
      });

      it('should handle lock/unlock error', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { locked: false, editable: true, country: 'USA' },
          { locked: false, editable: true, country: 'USA' }
        ];

        deviceService.updateDeviceState.and.returnValue(throwError(() => new Error('Lock Error')));
        spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);

        await expectAsync(service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('enableDisableDevices', () => {
      it('should enable/disable devices successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { editable: false, country: 'USA' },
          { editable: false, country: 'USA' }
        ];
        const mockResponse = { body: { message: 'Devices enabled successfully' } };

        deviceService.editEnableDisableForDevice.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');
        spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);

        await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

        expect(service.validateDeviceEnableDisablePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
        expect(deviceService.editEnableDisableForDevice).toHaveBeenCalledWith(mockDeviceIds, true);
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices enabled successfully');
      });

      it('should handle enable/disable error', async () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { editable: false, country: 'USA' },
          { editable: false, country: 'USA' }
        ];

        deviceService.editEnableDisableForDevice.and.returnValue(throwError(() => new Error('Enable Error')));
        spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);

        await expectAsync(service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('disableProductStatusForDevices', () => {
      it('should disable product status successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockResponse = { body: { message: 'Product status disabled' } };

        deviceService.disableProductStatusForDevice.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');

        await service.disableProductStatusForDevices(mockDeviceIds, [], DeviceListResource);

        expect(deviceService.disableProductStatusForDevice).toHaveBeenCalledWith(mockDeviceIds);
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Product status disabled');
      });

      it('should handle disable product status error', async () => {
        const mockDeviceIds = [1, 2];

        deviceService.disableProductStatusForDevice.and.returnValue(throwError(() => new Error('Disable Error')));

        await expectAsync(service.disableProductStatusForDevices(mockDeviceIds, [], DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('rmaProductStatusForDevices', () => {
      it('should set RMA product status successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockResponse = { body: { message: 'RMA status set' } };

        deviceService.rmaProductStatusForDevice.and.returnValue(of(mockResponse as any));
        spyOn(service.getDeviceListRefreshSubject(), 'next');

        await service.rmaProductStatusForDevices(mockDeviceIds, [], DeviceListResource);

        expect(deviceService.rmaProductStatusForDevice).toHaveBeenCalledWith(mockDeviceIds);
        expect(toastrServiceMock.success).toHaveBeenCalledWith('RMA status set');
      });

      it('should handle RMA product status error', async () => {
        const mockDeviceIds = [1, 2];

        deviceService.rmaProductStatusForDevice.and.returnValue(throwError(() => new Error('RMA Error')));

        await expectAsync(service.rmaProductStatusForDevices(mockDeviceIds, [], DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('associateDevicesWithSalesOrder', () => {
      it('should associate devices with sales order successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSalesOrderData = { salesOrderId: 'SO001', customerId: 123 };
        const mockResponse = { body: { message: 'Devices associated successfully' } };

        deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
        spyOn(service, 'updateSalesOrderCacheOnly').and.returnValue(Promise.resolve());
        spyOn(service.getDeviceListRefreshSubject(), 'next');

        await service.associateDevicesWithSalesOrder(mockDeviceIds, [], mockSalesOrderData, true, DeviceListResource);

        expect(deviceService.associationDeviceWithSalesOrder).toHaveBeenCalledWith(mockDeviceIds, jasmine.any(Object));
        expect(service.updateSalesOrderCacheOnly).toHaveBeenCalled();
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices associated successfully');
        expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
      });

      it('should associate devices without updating sales order cache when not new add', async () => {
        const mockDeviceIds = [1, 2];
        const mockSalesOrderData = { salesOrderId: 'SO001', customerId: 123 };
        const mockResponse = { body: { message: 'Devices associated successfully' } };

        deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
        spyOn(service, 'updateSalesOrderCacheOnly').and.returnValue(Promise.resolve());

        await service.associateDevicesWithSalesOrder(mockDeviceIds, [], mockSalesOrderData, false, DeviceListResource);

        expect(deviceService.associationDeviceWithSalesOrder).toHaveBeenCalledWith(mockDeviceIds, jasmine.any(Object));
        expect(service.updateSalesOrderCacheOnly).not.toHaveBeenCalled();
      });

      it('should handle association error', async () => {
        const mockDeviceIds = [1, 2];
        const mockSalesOrderData = { salesOrderId: 'SO001', customerId: 123 };

        deviceService.associationDeviceWithSalesOrder.and.returnValue(throwError(() => new Error('Association Error')));

        await expectAsync(service.associateDevicesWithSalesOrder(mockDeviceIds, [], mockSalesOrderData, true, DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });
  });

  describe('Device Selection and Validation Operations', () => {
    describe('validateDeviceSelection', () => {
      it('should return false when no devices are selected', () => {
        const result = service.validateDeviceSelection([], [], DeviceListResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
      });

      it('should return true when devices are selected and validation passes', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { editable: true, country: 'USA' },
          { editable: true, country: 'USA' }
        ];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

        const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
        expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalled();
      });

      it('should return false when validation fails', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { editable: false, country: 'USA' },
          { editable: true, country: 'USA' }
        ];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(result).toBe(false);
        expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
      });
    });

    describe('validateUserPermissionsAndCountry', () => {
      it('should validate permissions and country access successfully', () => {
        const mockSelectedDevices = [
          { editable: true, country: 'USA' },
          { editable: true, country: 'Canada' }
        ];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

        const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalledWith([true, true], DeviceListResource);
        expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalledWith(['USA', 'Canada'], DeviceListResource, true);
      });

      it('should return false when edit validation fails', () => {
        const mockSelectedDevices = [
          { editable: false, country: 'USA' }
        ];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

        expect(result).toBe(false);
        expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
      });

      it('should return false when country validation fails', () => {
        const mockSelectedDevices = [
          { editable: true, country: 'RESTRICTED_COUNTRY' }
        ];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(false);

        const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

        expect(result).toBe(false);
        expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalled();
      });
    });

    describe('validateUserCountryAccess', () => {
      it('should return false when no devices are selected', () => {
        const result = service.validateUserCountryAccess([], DeviceListResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
      });

      it('should validate country access successfully', () => {
        const mockSelectedDevices = [
          { country: 'USA' },
          { country: 'Canada' }
        ];

        moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

        const result = service.validateUserCountryAccess(mockSelectedDevices, DeviceListResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalledWith(['USA', 'Canada'], DeviceListResource, true);
      });
    });

    describe('getDeviceAssociatedCountries', () => {
      it('should extract countries from selected devices', () => {
        const mockSelectedDevices = [
          { country: 'USA', deviceId: 'DEV001' },
          { country: 'Canada', deviceId: 'DEV002' },
          { country: 'USA', deviceId: 'DEV003' }
        ];

        const result = service.getDeviceAssociatedCountries(mockSelectedDevices);

        expect(result).toEqual(['USA', 'Canada', 'USA']);
      });

      it('should return empty array for empty device list', () => {
        const result = service.getDeviceAssociatedCountries([]);

        expect(result).toEqual([]);
      });
    });

    describe('validateSingleDevicePermissions', () => {
      it('should validate single device permissions successfully', () => {
        const mockDevice = { editable: true, country: 'USA' };

        moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

        const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(true, DeviceDetailResource);
        expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
      });

      it('should return false when edit state validation fails', () => {
        const mockDevice = { editable: false, country: 'USA' };

        moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(false);

        const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

        expect(result).toBe(false);
        expect(moduleValidationService.validateWithEditStateForSingleRecord).toHaveBeenCalled();
      });

      it('should return false when country validation fails', () => {
        const mockDevice = { editable: true, country: 'RESTRICTED_COUNTRY' };

        moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(false);

        const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

        expect(result).toBe(false);
        expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalled();
      });
    });

    describe('validateSingleDeviceCountryAccess', () => {
      it('should validate single device country access successfully', () => {
        const mockDevice = { country: 'USA' };

        moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

        const result = service.validateSingleDeviceCountryAccess(mockDevice, DeviceDetailResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
      });

      it('should return false when country validation fails', () => {
        const mockDevice = { country: 'RESTRICTED_COUNTRY' };

        moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(false);

        const result = service.validateSingleDeviceCountryAccess(mockDevice, DeviceDetailResource);

        expect(result).toBe(false);
      });
    });
  });

  describe('Device Detail Operations', () => {
    describe('loadDeviceDetail', () => {
      it('should load device detail successfully', async () => {
        const mockDeviceId = 123;
        const mockResponse = {
          status: 200,
          body: {
            id: 123,
            deviceId: 'DEV001',
            releaseId: 456,
            salesOrderId: 789,
            deviceSerialNo: 'SN123456',
            country: 'USA'
          }
        };

        deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceDetail(mockDeviceId);

        expect(result.success).toBe(true);
        expect(result.deviceDetail).toEqual(jasmine.objectContaining({
          id: 123,
          deviceId: 'DEV001',
          releaseId: 456
        }));
        expect(result.releaseVersionId).toBe(456);
        expect(result.transferProductDetails).toBeDefined();
        expect(result.transferProductDetails).toEqual(jasmine.any(TransferProductDetails));
      });

      it('should handle device detail with null releaseId', async () => {
        const mockDeviceId = 123;
        const mockResponse = {
          status: 200,
          body: {
            id: 123,
            deviceId: 'DEV001',
            releaseId: null,
            salesOrderId: 789,
            deviceSerialNo: 'SN123456',
            country: 'USA'
          }
        };

        deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceDetail(mockDeviceId);

        expect(result.success).toBe(true);
        expect(result.releaseVersionId).toBe(-1);
      });

      it('should handle non-200 response', async () => {
        const mockDeviceId = 123;
        const mockResponse = { status: 404, body: null };

        deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceDetail(mockDeviceId);

        expect(result.success).toBe(false);
        expect(result.deviceDetail).toBe(null);
        expect(result.releaseVersionId).toBe(-1);
        expect(result.transferProductDetails).toBe(null);
      });

      it('should handle API error', async () => {
        const mockDeviceId = 123;
        const errorResponse = new Error('Detail Error');

        deviceService.getDeviceDetail.and.returnValue(throwError(() => errorResponse));

        await expectAsync(service.loadDeviceDetail(mockDeviceId)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('assignReleaseVersion', () => {
      it('should assign release version successfully', async () => {
        const mockDeviceId = 123;
        const mockReleaseVersionId = 456;
        const mockResponse = { status: 200, body: { message: 'Success' } };

        deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

        await service.assignReleaseVersion(mockDeviceId, mockReleaseVersionId);

        expect(deviceService.assignSelectedReleaseVersion).toHaveBeenCalledWith(jasmine.any(AssignSelectedReleaseVersionRequest));
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Release version assigned successfully');
      });

      it('should handle non-200 response', async () => {
        const mockDeviceId = 123;
        const mockReleaseVersionId = 456;
        const mockResponse = { status: 400, body: { message: 'Error' } };

        deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

        await service.assignReleaseVersion(mockDeviceId, mockReleaseVersionId);

        expect(toastrServiceMock.error).toHaveBeenCalledWith('Error in assigning Release version');
      });
    });

    describe('shouldDisableAssignButton', () => {
      it('should return true when no release version is selected', () => {
        const result = service.shouldDisableAssignButton(-1, 123);
        expect(result).toBe(true);
      });

      it('should return true when selected version equals current version', () => {
        const result = service.shouldDisableAssignButton(123, 123);
        expect(result).toBe(true);
      });

      it('should return false when different version is selected', () => {
        const result = service.shouldDisableAssignButton(456, 123);
        expect(result).toBe(false);
      });
    });

    describe('getReleaseVersions', () => {
      it('should get release versions for test device with update permission', async () => {
        const mockResponse = { body: [{ id: 1, version: 'v1.0.0' }, { id: 2, version: 'v2.0.0' }] };

        deviceService.getReleaseVersionDetail.and.returnValue(of(mockResponse as any));

        const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

        expect(result.success).toBe(true);
        expect(result.releaseVersions).toEqual(mockResponse.body);
        expect(result.selectedReleaseVersion).toBe(-1);
        expect(result.btnReleaseVersionDisable).toBe(true);
        expect(deviceService.getReleaseVersionDetail).toHaveBeenCalledWith(jasmine.any(ReleaseVersionRequest));
      });

      it('should return empty result for test device without country', async () => {
        const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, null as any, 'v1.0.0', true);

        expect(result.success).toBe(true);
        expect(result.releaseVersions).toEqual([]);
        expect(result.selectedReleaseVersion).toBe(-1);
        expect(result.btnReleaseVersionDisable).toBe(true);
        expect(deviceService.getReleaseVersionDetail).not.toHaveBeenCalled();
      });

      it('should return empty result for non-test device', async () => {
        const result = await service.getReleaseVersions(deviceTypesEnum.CLIENT_DEVICE, 1, 'v1.0.0', true);

        expect(result.success).toBe(true);
        expect(result.releaseVersions).toEqual([]);
        expect(result.selectedReleaseVersion).toBe(-1);
        expect(result.btnReleaseVersionDisable).toBe(true);
        expect(deviceService.getReleaseVersionDetail).not.toHaveBeenCalled();
      });

      it('should return empty result for test device without update permission', async () => {
        const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', false);

        expect(result.success).toBe(true);
        expect(result.releaseVersions).toEqual([]);
        expect(result.selectedReleaseVersion).toBe(-1);
        expect(result.btnReleaseVersionDisable).toBe(true);
        expect(deviceService.getReleaseVersionDetail).not.toHaveBeenCalled();
      });

      it('should handle API error and return empty result', async () => {
        deviceService.getReleaseVersionDetail.and.returnValue(throwError(() => new Error('Release Version Error')));

        const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

        expect(result.success).toBe(true);
        expect(result.releaseVersions).toEqual([]);
        expect(result.selectedReleaseVersion).toBe(-1);
        expect(result.btnReleaseVersionDisable).toBe(true);
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });
  });

  describe('Device Association Operations', () => {
    describe('associateDevicesWithSalesOrder', () => {
      it('should associate devices with sales order successfully', async () => {
        const mockDeviceIds = [1, 2];
        const mockSalesOrderData = {
          salesOrderNumber: 'SO001',
          customerName: 'Test Customer',
          customerEmail: '<EMAIL>',
          countryId: 1,
          poNumber: null,
          deviceAutoLock: true,
          probeAutoLock: false,
          orderRecordType: null,
          salesForceOrder: false,
          soLetterDownload: false
        };
        const mockResponse = { body: { message: 'Association successful' } };

        deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
        spyOn(service, 'updateSalesOrderCacheOnly').and.returnValue(Promise.resolve());
        spyOn(service, 'updateCacheInBackground').and.returnValue(Promise.resolve());
        spyOn(service.getDeviceListRefreshSubject(), 'next');

        await service.associateDevicesWithSalesOrder(mockDeviceIds, [], mockSalesOrderData, true, DeviceListResource);

        expect(deviceService.associationDeviceWithSalesOrder).toHaveBeenCalledWith(mockDeviceIds, mockSalesOrderData);
        expect(service.updateSalesOrderCacheOnly).toHaveBeenCalled();
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Association successful');
      });

      it('should not update sales order cache when not new add', async () => {
        const mockDeviceIds = [1, 2];
        const mockSalesOrderData = {
          salesOrderNumber: 'SO001',
          customerName: 'Test Customer'
        } as any;
        const mockResponse = { body: { message: 'Association successful' } };

        deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
        spyOn(service, 'updateSalesOrderCacheOnly').and.returnValue(Promise.resolve());
        spyOn(service, 'updateCacheInBackground').and.returnValue(Promise.resolve());

        await service.associateDevicesWithSalesOrder(mockDeviceIds, [], mockSalesOrderData, false, DeviceListResource);

        expect(service.updateSalesOrderCacheOnly).not.toHaveBeenCalled();
      });

      it('should handle association error', async () => {
        const mockDeviceIds = [1, 2];
        const mockSalesOrderData = { salesOrderNumber: 'SO001' } as any;

        deviceService.associationDeviceWithSalesOrder.and.returnValue(throwError(() => new Error('Association Error')));

        await expectAsync(service.associateDevicesWithSalesOrder(mockDeviceIds, [], mockSalesOrderData, false, DeviceListResource)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });
  });

  describe('Device Selection and Validation Logic', () => {
    describe('validateDeviceSelection', () => {
      it('should return false when no devices selected', () => {
        const result = service.validateDeviceSelection([], [], DeviceListResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
      });

      it('should validate permissions and country when devices selected', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ editable: true, country: 'USA' }];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

        const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
        expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalled();
      });

      it('should return false when validation fails', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [{ editable: false, country: 'USA' }];

        moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(result).toBe(false);
      });
    });

    describe('validateSingleDevicePermissions', () => {
      it('should validate single device permissions successfully', () => {
        const mockDevice = { editable: true, country: 'USA' };

        moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(true);
        moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

        const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

        expect(result).toBe(true);
        expect(moduleValidationService.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(true, DeviceDetailResource);
        expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
      });

      it('should return false when edit state validation fails', () => {
        const mockDevice = { editable: false, country: 'USA' };

        moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(false);

        const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

        expect(result).toBe(false);
        expect(moduleValidationService.validateWithUserCountryForSingleRecord).not.toHaveBeenCalled();
      });
    });

    describe('validateDevicePermissions', () => {
      it('should use validateDeviceSelection for DeviceListResource', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { editable: true, country: 'USA' },
          { editable: true, country: 'Canada' }
        ];

        spyOn(service, 'validateDeviceSelection').and.returnValue(true);

        const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceListResource);

        expect(result).toBe(true);
        expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
      });

      it('should use validateSingleDevicePermissions for DeviceDetailResource', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ editable: true, country: 'USA' }];

        spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

        const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

        expect(result).toBe(true);
        expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
      });

      it('should return false for unknown resource', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ editable: true, country: 'USA' }];

        const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, 'UnknownResource');

        expect(result).toBe(false);
      });
    });

    describe('validateDeviceLockUnlockPermissions', () => {
      it('should use validateDeviceSelection for DeviceListResource', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { locked: false, editable: true, country: 'USA' },
          { locked: false, editable: true, country: 'Canada' }
        ];

        spyOn(service, 'validateDeviceSelection').and.returnValue(true);

        const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

        expect(result).toBe(true);
        expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
      });

      it('should validate single device permissions for DeviceDetailResource', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

        spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

        const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

        expect(result).toBe(true);
        expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
      });

      it('should return false when device is already locked and trying to lock', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ locked: true, editable: true, country: 'USA' }];

        spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

        const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Device already locked');
      });

      it('should return false when device is already unlocked and trying to unlock', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

        spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

        const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Device already unlocked');
      });

      it('should return false for unknown resource', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

        const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, 'UnknownResource');

        expect(result).toBe(false);
      });
    });

    describe('validateDeviceEnableDisablePermissions', () => {
      it('should use validateUserCountryAccess for DeviceListResource', () => {
        const mockDeviceIds = [1, 2];
        const mockSelectedDevices = [
          { editable: false, country: 'USA' },
          { editable: false, country: 'Canada' }
        ];

        spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

        const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

        expect(result).toBe(true);
        expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
      });

      it('should validate single device country access for DeviceDetailResource', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ editable: false, country: 'USA' }];

        spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

        const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

        expect(result).toBe(true);
        expect(service.validateSingleDeviceCountryAccess).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
      });

      it('should return false when device is already enabled and trying to enable', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ editable: true, country: 'USA' }];

        spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

        const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Device is already marked as editable.');
      });

      it('should return false when device is already disabled and trying to disable', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ editable: false, country: 'USA' }];

        spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

        const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Device is already marked as read-only.');
      });

      it('should return false for unknown resource', () => {
        const mockDeviceIds = [1];
        const mockSelectedDevices = [{ editable: false, country: 'USA' }];

        const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, 'UnknownResource');

        expect(result).toBe(false);
      });
    });

    describe('getDeviceAssociatedCountries', () => {
      it('should extract countries from selected devices', () => {
        const mockSelectedDevices = [
          { country: 'USA' },
          { country: 'Canada' },
          { country: 'USA' }
        ];

        const result = service.getDeviceAssociatedCountries(mockSelectedDevices);

        expect(result).toEqual(['USA', 'Canada', 'USA']);
      });
    });
  });

  describe('Device Detail Operations', () => {
    describe('loadDeviceDetail', () => {
      it('should load device detail successfully', async () => {
        const mockDeviceId = 123;
        const mockDeviceDetail = {
          id: 123,
          deviceId: 'DEV001',
          salesOrderId: 456,
          deviceSerialNo: 'SN001',
          releaseId: 789
        };
        const mockResponse = { status: 200, body: mockDeviceDetail };

        deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceDetail(mockDeviceId);

        expect(result.success).toBe(true);
        expect(result.deviceDetail).toEqual(jasmine.objectContaining({
          id: 123,
          deviceId: 'DEV001',
          salesOrderId: 456,
          deviceSerialNo: 'SN001',
          releaseId: 789
        }));
        expect(result.releaseVersionId).toBe(789);
        expect(result.transferProductDetails).toBeInstanceOf(TransferProductDetails);
      });

      it('should handle null release ID', async () => {
        const mockDeviceId = 123;
        const mockDeviceDetail = {
          id: 123,
          deviceId: 'DEV001',
          salesOrderId: 456,
          deviceSerialNo: 'SN001',
          releaseId: null
        };
        const mockResponse = { status: 200, body: mockDeviceDetail };

        deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceDetail(mockDeviceId);

        expect(result.success).toBe(true);
        expect(result.releaseVersionId).toBe(-1);
      });

      it('should handle non-200 response', async () => {
        const mockDeviceId = 123;
        const mockResponse = { status: 404, body: null };

        deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceDetail(mockDeviceId);

        expect(result.success).toBe(false);
        expect(result.deviceDetail).toBe(null);
        expect(result.releaseVersionId).toBe(-1);
        expect(result.transferProductDetails).toBe(null);
      });

      it('should handle API error', async () => {
        const mockDeviceId = 123;

        deviceService.getDeviceDetail.and.returnValue(throwError(() => new Error('Detail Error')));

        await expectAsync(service.loadDeviceDetail(mockDeviceId)).toBeRejected();
        expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('assignReleaseVersion', () => {
      it('should assign release version successfully', async () => {
        const mockDeviceId = 123;
        const mockReleaseVersionId = 456;
        const mockResponse = { status: 200 };

        deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

        await service.assignReleaseVersion(mockDeviceId, mockReleaseVersionId);

        expect(deviceService.assignSelectedReleaseVersion).toHaveBeenCalledWith(
          new AssignSelectedReleaseVersionRequest(mockDeviceId, mockReleaseVersionId));
        expect(toastrServiceMock.success).toHaveBeenCalledWith('Release version assigned successfully');
      });

      it('should handle non-200 response', async () => {
        const mockDeviceId = 123;
        const mockReleaseVersionId = 456;
        const mockResponse = { status: 400 };

        deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

        await service.assignReleaseVersion(mockDeviceId, mockReleaseVersionId);

        expect(toastrServiceMock.error).toHaveBeenCalledWith('Error in assigning Release version');
      });
    });

    describe('shouldDisableAssignButton', () => {
      it('should return true when no release version selected', () => {
        const result = service.shouldDisableAssignButton(-1, 123);
        expect(result).toBe(true);
      });

      it('should return true when selected version equals current version', () => {
        const result = service.shouldDisableAssignButton(123, 123);
        expect(result).toBe(true);
      });

      it('should return false when different version selected', () => {
        const result = service.shouldDisableAssignButton(456, 123);
        expect(result).toBe(false);
      });
    });
  });

  describe('Filter Integration Logic', () => {
    describe('validateFilterForm', () => {
      it('should return true when at least one filter field has value', () => {
        const mockFormValue = {
          deviceId: 'DEV001',
          deviceSerialNo: '',
          customerName: '',
          packageVersions: null,
          connectionState: null,
          deviceLockState: null,
          deviceEditState: null,
          countries: null,
          drpDeviceType: null,
          salesOrderNumber: null,
          productStatus: null
        };

        commonsService.checkValueIsNullOrEmpty.and.callFake((value) => !value);

        const result = service.validateFilterForm(mockFormValue);

        expect(result).toBe(true);
      });

      it('should return false when all filter fields are empty', () => {
        const mockFormValue = {
          deviceId: '',
          deviceSerialNo: '',
          customerName: '',
          packageVersions: null,
          connectionState: null,
          deviceLockState: null,
          deviceEditState: null,
          countries: null,
          drpDeviceType: null,
          salesOrderNumber: null,
          productStatus: null
        };

        commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

        const result = service.validateFilterForm(mockFormValue);

        expect(result).toBe(false);
      });
    });

    describe('buildDeviceSearchRequest', () => {
      it('should build device search request from form values', () => {
        const mockFormValue = {
          deviceId: 'DEV001',
          deviceSerialNo: 'SN001',
          customerName: 'Customer1',
          packageVersions: ['v1.0.0'],
          connectionState: [{ value: 'ONLINE' }],
          deviceLockState: [{ value: true }],
          deviceEditState: [{ value: false }],
          countries: [{ id: 1, country: 'USA' }],
          drpDeviceType: 'TEST_DEVICE',
          salesOrderNumber: ['SO001'],
          productStatus: [{ value: 'ENABLED' }]
        };

        commonsService.checkNullFieldValue.and.callFake((value) => value || null);
        commonsService.getIdsFromArray.and.returnValue([1]);
        commonsService.getSelectedValueFromEnum.and.returnValue(['ENABLED']);
        commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(true);
        commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST_DEVICE');

        const result = service.buildDeviceSearchRequest(mockFormValue);

        expect(result).toBeInstanceOf(DeviceSearchRequest);
        expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('DEV001');
        expect(commonsService.getIdsFromArray).toHaveBeenCalled();
        expect(commonsService.getSelectedValueFromEnum).toHaveBeenCalled();
      });
    });

    describe('processFilterSearch', () => {
      it('should process filter search successfully', () => {
        const mockFormValue = { deviceId: 'DEV001' };
        const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

        spyOn(service, 'validateFilterForm').and.returnValue(true);
        spyOn(service, 'buildDeviceSearchRequest').and.returnValue(new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null));
        spyOn(service, 'callDeviceListFilterRequestParameterSubject');

        const result = service.processFilterSearch(mockFormValue, false, mockListingParam);

        expect(result).toBe(true);
        expect(service.validateFilterForm).toHaveBeenCalledWith(mockFormValue);
        expect(service.buildDeviceSearchRequest).toHaveBeenCalledWith(mockFormValue);
        expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
      });

      it('should return false when form is invalid', () => {
        const mockFormValue = {};
        const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

        const result = service.processFilterSearch(mockFormValue, true, mockListingParam);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
      });

      it('should return false when validation fails', () => {
        const mockFormValue = {};
        const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

        spyOn(service, 'validateFilterForm').and.returnValue(false);

        const result = service.processFilterSearch(mockFormValue, false, mockListingParam);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
      });
    });

    describe('clearAllFiltersAndRefresh', () => {
      it('should clear filters and refresh device list', () => {
        const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

        spyOn(service, 'callDeviceListFilterRequestParameterSubject');

        service.clearAllFiltersAndRefresh(mockListingParam);

        expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
          jasmine.objectContaining({
            deviceSearchRequest: jasmine.objectContaining({
              packageVersions: null
            })
          })
        );
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty device arrays in validation', () => {
      const result = service.validateDeviceSelection([], [], DeviceListResource);
      expect(result).toBe(false);
      expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
    });

    it('should handle null device objects in validation', () => {
      moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(false);

      const result = service.validateSingleDevicePermissions(null, DeviceDetailResource);
      expect(result).toBe(false);
    });

    it('should handle undefined form values in filter validation', () => {
      commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

      const result = service.validateFilterForm(undefined);
      expect(result).toBe(false);
    });

    describe('buildDeviceSearchRequest', () => {
      it('should build device search request with all form values', () => {
        const mockFormValue = {
          deviceId: 'DEV001',
          customerName: 'Test Customer',
          deviceSerialNo: 'SN123',
          countries: [{ id: 1, country: 'USA' }],
          productStatus: [{ value: 'ENABLED' }],
          connectionState: [{ value: 'CONNECTED' }],
          deviceLockState: [{ key: true, value: 'Locked' }],
          deviceEditState: [{ key: false, value: 'Read Only' }],
          packageVersions: ['v1.0.0'],
          drpDeviceType: 'TEST_DEVICE',
          salesOrderNumber: ['SO001']
        };

        commonsService.checkNullFieldValue.and.callFake((value) => value || null);
        commonsService.getIdsFromArray.and.returnValue([1]);
        commonsService.getSelectedValueFromEnum.and.returnValue(['ENABLED']);
        commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(true);
        commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST_DEVICE');

        const result = service.buildDeviceSearchRequest(mockFormValue);

        expect(result).toBeInstanceOf(DeviceSearchRequest);
        expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('DEV001');
        expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('Test Customer');
        expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('SN123');
        expect(commonsService.getIdsFromArray).toHaveBeenCalledWith([{ id: 1, country: 'USA' }]);
        expect(commonsService.getSelectedValueFromEnum).toHaveBeenCalledWith(jasmine.any(Array));
        expect(commonsService.getSelectedValueFromBooleanKeyValueMapping).toHaveBeenCalledWith(jasmine.any(Array));
        expect(commonsService.getDeviceTypeStringToEnum).toHaveBeenCalledWith('TEST_DEVICE');
      });

      it('should handle null and undefined form values', () => {
        const mockFormValue = {
          deviceId: null,
          customerName: undefined,
          deviceSerialNo: '',
          countries: null,
          productStatus: undefined,
          connectionState: [],
          deviceLockState: null,
          deviceEditState: undefined,
          packageVersions: null,
          drpDeviceType: null,
          salesOrderNumber: undefined
        };

        commonsService.checkNullFieldValue.and.returnValue(null);
        commonsService.getSelectedValueFromEnum.and.returnValue([]);
        commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
        commonsService.getDeviceTypeStringToEnum.and.returnValue(null);

        const result = service.buildDeviceSearchRequest(mockFormValue);

        expect(result).toBeInstanceOf(DeviceSearchRequest);
      });
    });

    describe('processFilterSearch', () => {
      it('should process filter search successfully', () => {
        const mockFormValue = {
          deviceId: 'DEV001',
          customerName: 'Test Customer'
        };
        const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

        commonsService.checkValueIsNullOrEmpty.and.returnValue(false);
        commonsService.checkNullFieldValue.and.callFake((value) => value || null);
        commonsService.getSelectedValueFromEnum.and.returnValue([]);
        commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
        commonsService.getDeviceTypeStringToEnum.and.returnValue(null);
        spyOn(service, 'callDeviceListFilterRequestParameterSubject');

        const result = service.processFilterSearch(mockFormValue, false, mockListingPageReloadSubjectParameter);

        expect(result).toBe(true);
        expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(jasmine.any(DeviceFilterAction));
      });

      it('should return false when form is invalid', () => {
        const mockFormValue = {};
        const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

        const result = service.processFilterSearch(mockFormValue, true, mockListingPageReloadSubjectParameter);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
      });

      it('should return false when filter validation fails', () => {
        const mockFormValue = {};
        const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

        commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

        const result = service.processFilterSearch(mockFormValue, false, mockListingPageReloadSubjectParameter);

        expect(result).toBe(false);
        expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
      });
    });

    describe('clearAllFiltersAndRefresh', () => {
      it('should clear all filters and refresh device list', () => {
        const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
        spyOn(service, 'callDeviceListFilterRequestParameterSubject');

        service.clearAllFiltersAndRefresh(mockListingPageReloadSubjectParameter);

        expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
          jasmine.objectContaining({
            deviceSearchRequest: jasmine.objectContaining({
              packageVersions: null,
              status: null,
              deviceType: null,
              deviceId: null,
              deviceSerialNo: null,
              customerName: null,
              countryIds: null,
              deviceLockStatus: null,
              isEditable: null,
              salesOrderNumbers: null,
              productStatus: null
            })
          })
        );
      });
    });

    describe('Private method coverage through processDeviceListResponse', () => {
      it('should process device list response correctly', async () => {
        const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        const mockPageObj = { page: 0, size: 10 };
        const mockResponse = {
          status: 200,
          body: {
            content: [
              { id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED' },
              { id: 2, deviceId: 'DEV002', editable: false, country: 'Canada', deviceType: 'CLIENT_DEVICE', locked: true, productStatus: 'DISABLED' }
            ],
            numberOfElements: 2,
            totalElements: 100
          }
        };

        deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

        expect(result.success).toBe(true);
        expect(result.devices).toEqual(mockResponse.body.content);
        expect(result.totalDeviceDisplay).toBe(2);
        expect(result.totalDevice).toBe(100);
        expect(result.localDeviceList).toEqual([
          { id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED' },
          { id: 2, deviceId: 'DEV002', editable: false, country: 'Canada', deviceType: 'CLIENT_DEVICE', locked: true, productStatus: 'DISABLED' }
        ]);
      });

      it('should handle empty device list response', async () => {
        const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        const mockPageObj = { page: 0, size: 10 };
        const mockResponse = {
          status: 200,
          body: {
            content: [],
            numberOfElements: 0,
            totalElements: 0
          }
        };

        deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

        const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

        expect(result.success).toBe(true);
        expect(result.devices).toEqual([]);
        expect(result.totalDeviceDisplay).toBe(0);
        expect(result.totalDevice).toBe(0);
        expect(result.localDeviceList).toEqual([]);
      });


    });
  });
});
